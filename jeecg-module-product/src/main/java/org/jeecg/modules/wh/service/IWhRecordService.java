package org.jeecg.modules.wh.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.wh.entity.WhRecord;
import org.jeecg.modules.wh.dto.WhRecordQueryDTO;
import org.jeecg.modules.wh.dto.WhRecordSaveDTO;
import org.jeecg.modules.wh.vo.WhRecordVO;

import java.util.List;

/**
 * @Description: 用户门户记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
public interface IWhRecordService extends IService<WhRecord> {

    /**
     * 保存用户记录
     * @param saveDTO 保存参数
     * @return 操作结果
     */
    boolean saveUserRecord(WhRecordSaveDTO saveDTO);

    /**
     * 删除用户记录
     * @param userId 用户ID
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param recordType 记录类型
     * @return 操作结果
     */
    boolean deleteUserRecord(String userId, String contentId, Integer contentType, Integer recordType);

    /**
     * 检查用户记录是否存在
     * @param userId 用户ID
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param recordType 记录类型
     * @return 是否存在
     */
    boolean existsUserRecord(String userId, String contentId, Integer contentType, Integer recordType);

    /**
     * 根据查询条件获取用户记录列表（带标题信息）
     * @param queryDTO 查询条件
     * @return 记录列表
     */
    List<WhRecordVO> getRecordListWithTitle(WhRecordQueryDTO queryDTO);
}
