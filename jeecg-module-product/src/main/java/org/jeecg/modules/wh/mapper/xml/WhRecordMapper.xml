<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.wh.mapper.WhRecordMapper">

    <!-- 检查记录是否存在 -->
    <select id="findExistRecord" resultType="org.jeecg.modules.wh.entity.WhRecord">
        SELECT * FROM wh_record
        WHERE user_id = #{userId} AND content_id = #{contentId}
        AND content_type = #{contentType} AND record_type = #{recordType}
        LIMIT 1
    </select>

    <!-- 根据查询条件获取用户记录列表（带标题信息） -->
    <select id="getRecordListWithTitle" resultType="org.jeecg.modules.wh.vo.WhRecordVO">
        SELECT
            wr.id,
            wr.content_id,
            wr.content_type,
            wr.create_time,
            CASE
                WHEN wr.content_type = 1 THEN wp.name
                WHEN wr.content_type = 2 THEN an.name
                WHEN wr.content_type = 3 THEN st.name
                ELSE ''
            END AS title
        FROM wh_record wr
        LEFT JOIN wh_products wp ON wr.content_type = 1 AND wr.content_id = wp.id
        LEFT JOIN app_news an ON wr.content_type = 2 AND wr.content_id = an.id
        LEFT JOIN sys_tenant st ON wr.content_type = 3 AND wr.content_id = CAST(st.id AS CHAR)
        WHERE 1=1
        <if test="queryDTO.userId != null and queryDTO.userId != ''">
            AND wr.user_id = #{queryDTO.userId}
        </if>
        <if test="queryDTO.recordType != null">
            AND wr.record_type = #{queryDTO.recordType}
        </if>
        <if test="queryDTO.contentType != null">
            AND wr.content_type = #{queryDTO.contentType}
        </if>
        <if test="queryDTO.startTime != null">
            AND wr.create_time >= #{queryDTO.startTime}
        </if>
        <if test="queryDTO.endTime != null">
            AND wr.create_time &lt;= #{queryDTO.endTime}
        </if>
        <if test="queryDTO.title != null and queryDTO.title != ''">
            AND (
                (wr.content_type = 1 AND wp.name LIKE CONCAT('%', #{queryDTO.title}, '%'))
                OR (wr.content_type = 2 AND an.name LIKE CONCAT('%', #{queryDTO.title}, '%'))
                OR (wr.content_type = 3 AND st.name LIKE CONCAT('%', #{queryDTO.title}, '%'))
            )
        </if>
        ORDER BY wr.create_time DESC
    </select>

</mapper>
