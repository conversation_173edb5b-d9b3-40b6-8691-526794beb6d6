package org.jeecg.modules.wh.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.wh.entity.WhRecordStatistics;
import org.jeecg.modules.wh.dto.WhRecordStatisticsDTO;
import org.jeecg.modules.wh.vo.WhRecordStatisticsVO;

/**
 * @Description: 用户门户记录统计表
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
public interface IWhRecordStatisticsService extends IService<WhRecordStatistics> {

    /**
     * 增加记录统计数量
     * @param dto 统计参数
     * @return 操作结果
     */
    boolean incrementCount(WhRecordStatisticsDTO dto);

    /**
     * 减少记录统计数量
     * @param dto 统计参数
     * @return 操作结果
     */
    boolean decrementCount(WhRecordStatisticsDTO dto);

    /**
     * 根据IP增加记录统计数量（未登录用户）
     * @param dto 统计参数
     * @param userIp 用户IP
     * @return 操作结果
     */
    boolean incrementCountByIp(WhRecordStatisticsDTO dto, String userIp);

    /**
     * 根据内容ID获取统计信息
     * @param pid 内容ID
     * @return 统计信息
     */
    WhRecordStatisticsVO getStatisticsByPid(String pid);
}
