package org.jeecg.modules.corp.vo.hour;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 小时维度点击数据VO
 */
@Data
@Accessors(chain = true)
public class HourlyClickVO implements Serializable {

    @ApiModelProperty(value = "小时值(0-23)，按天/周/月汇总时为null")
    private Integer hour;

    @ApiModelProperty(value = "统计日期")
    private String statDate;

    @ApiModelProperty(value = "数据类型:0-查询对象;1-对比对象")
    private String dataType;

    @ApiModelProperty(value = "点击PV")
    private Integer clickPv;

    @ApiModelProperty(value = "点击数")
    private Integer clickNum;

    @ApiModelProperty(value = "周期值，用于按天/周/月汇总时的标识")
    private String periodValue;
}
