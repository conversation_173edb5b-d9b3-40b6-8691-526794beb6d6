package org.jeecg.modules.corp.vo.hour;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ReportHourlyListVO implements Serializable {

    @ApiModelProperty(value = "时间维度显示值 小时:'08:00-09:00' 天:'2025-06-14' 周:'2025年第24周' 月:'2025-06'", example = "08:00-09:00")
    private String timeFrame;

    @ApiModelProperty(value = "当期点击PV")
    private Integer currentClickPv;

    @ApiModelProperty(value = "基期点击PV")
    private Integer baseClickPv;

    @ApiModelProperty(value = "点击PV环比增长率(%) 计算公式: ((currentClickPv - baseClickPv) / baseClickPv) * 100", example = "15.5")
    private String clickPvGrowthRate;

    @ApiModelProperty(value = "当期点击数")
    private Integer currentClickNum;

    @ApiModelProperty(value = "基期点击数")
    private Integer baseClickNum;

    @ApiModelProperty(value = "点击数环比增长率(%) 计算公式: ((currentClickNum - baseClickNum) / baseClickNum) * 100", example = "12.3")
    private String clickNumGrowthRate;

    @ApiModelProperty(value = "当期预约数")
    private Integer currentReservationNum;

    @ApiModelProperty(value = "基期预约数")
    private Integer baseReservationNum;

    @ApiModelProperty(value = "预约数环比增长率(%) 计算公式: ((currentReservationNum - baseReservationNum) / baseReservationNum) * 100", example = "8.7")
    private String reservationGrowthRate;

    @ApiModelProperty(value = "当期转化率 计算公式: (currentReservationNum / currentClickNum) * 100")
    private String currentConversionRate;

    @ApiModelProperty(value = "基期转化率 计算公式: (baseReservationNum / baseClickNum) * 100")
    private String baseConversionRate;
}
