package org.jeecg.modules.corp.vo.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("坐标信息视图对象")
public class ReportCoordinateVO implements Serializable {

    @ApiModelProperty(value = "行政区划代码", example = "130000")
    private String adcode;

    @ApiModelProperty(value = "中心坐标 [经度, 纬度]", example = "[114.502461, 38.045474]")
    private List<Double> center;

    @ApiModelProperty(value = "质心坐标 [经度, 纬度]", example = "[114.502461, 38.045474]")
    private List<Double> centroid;

    @ApiModelProperty(value = "城市", example = "石家庄市")
    private String name;
}