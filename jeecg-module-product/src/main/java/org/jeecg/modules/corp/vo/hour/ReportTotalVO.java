package org.jeecg.modules.corp.vo.hour;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecg.modules.corp.vo.report.ReportCurveVO;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class ReportTotalVO implements Serializable {

    @ApiModelProperty(value = "时段数据")
    private List<ReportHourlyVO> periodData;

    @ApiModelProperty(value = "查询日期与对比日期环比数据")
    private List<ReportHourlyListVO> monthOnMonthData;

    @ApiModelProperty(value = "汇总数据(访问量、预约人次、转化率)")
    private ReportSummaryVO summaryData;

}
