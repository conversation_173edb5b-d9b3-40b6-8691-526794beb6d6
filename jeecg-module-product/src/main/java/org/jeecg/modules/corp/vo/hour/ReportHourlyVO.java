package org.jeecg.modules.corp.vo.hour;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
@Data
@Accessors(chain = true)
public class ReportHourlyVO implements Serializable {

    @ApiModelProperty(value = "时段;若 dto 为天,则为天来汇总,为周同理",example = "08:00-09:00")
    private String timeFrame;

    @ApiModelProperty(value = "点击pv")
    private String  clickPv;

    @ApiModelProperty(value = "点击数即访问量")
    private String  clickNum;

    @ApiModelProperty(value = "预约总数")
    private String  reservationNum;

    @ApiModelProperty(value = "转化率=预约总数/点击数")
    private BigDecimal reservationRate;
}