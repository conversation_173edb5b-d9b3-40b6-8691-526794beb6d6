package org.jeecg.modules.corp.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class PdRecodeVO implements Serializable {

    @ApiModelProperty(value = "访问量 PV")
    private Integer clickPv;

    @ApiModelProperty(value = "访问量 PV 较昨日；0-下降，1-上升")
    private Integer clickPvCompare = 1;

    @ApiModelProperty(value = "独立访客 UV")
    private Integer clickNum;

    @ApiModelProperty(value = "独立访客 UV 较昨日；0-下降，1-上升")
    private Integer clickNumCompare = 1;

    @ApiModelProperty(value = "跳出率(%)")
    private BigDecimal bounceRate;

    @ApiModelProperty(value = "跳出率较昨日；0-下降，1-上升")
    private Integer bounceRateCompare = 1;

    @ApiModelProperty(value = "转化率(%)")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "转化率较昨日；0-下降，1-上升")
    private Integer conversionRateCompare = 1;

    @ApiModelProperty(value = "表单提交数")
    private Integer formSubmissions;

    @ApiModelProperty(value = "表单提交数较昨日；0-下降，1-上升")
    private Integer formSubmissionsCompare = 1;

    @ApiModelProperty(value = "点击率(%)")
    private BigDecimal ctr;

    @ApiModelProperty(value = "点击率较昨日；0-下降，1-上升")
    private Integer ctrCompare = 1;

    @ApiModelProperty(value = "平均停留时长(秒)")
    private BigDecimal avgStayTime;

    @ApiModelProperty(value = "平均停留时长较昨日；0-下降，1-上升")
    private Integer avgStayTimeCompare = 1;

    @ApiModelProperty(value = "返回率(%)")
    private BigDecimal returnRate;

    @ApiModelProperty(value = "返回率较昨日；0-下降，1-上升")
    private Integer returnRateCompare = 1;

    @ApiModelProperty(value = "内容完成率(%)")
    private BigDecimal completionRate;

    @ApiModelProperty(value = "内容完成率较昨日；0-下降，1-上升")
    private Integer completionRateCompare = 1;
}
