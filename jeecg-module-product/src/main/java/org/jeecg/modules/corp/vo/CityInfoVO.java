package org.jeecg.modules.corp.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 城市信息VO
 * 用于封装城市编码、车牌、完整城市名称、IP地址等信息
 */
@Data
@Accessors(chain = true)
public class CityInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市编码层级信息（包含一级和二级城市编码，格式：一级编码,二级编码）
     */
    private String cityCodeLevel;

    /**
     * 完整城市名称
     */
    private String fullCityName;

    /**
     * 顶级城市名称（省份）
     */
    private String topCityName;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * IP地址
     */
    private String ipAddress;
}
