package org.jeecg.modules.corp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 批量导入结果VO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@ApiModel(value = "BatchImportResultVO", description = "批量导入结果VO")
public class BatchImportResultVO {

    @ApiModelProperty(value = "是否成功")
    private boolean success;

    @ApiModelProperty(value = "消息")
    private String message;

    @ApiModelProperty(value = "成功导入的条数")
    private int successCount;

    @ApiModelProperty(value = "失败的条数")
    private int failureCount;

    @ApiModelProperty(value = "失败详情列表")
    private List<BatchImportFailureDetail> failureDetails;

    /**
     * 批量导入失败详情
     */
    @Data
    @ApiModel(value = "BatchImportFailureDetail", description = "批量导入失败详情")
    public static class BatchImportFailureDetail {

        @ApiModelProperty(value = "表格行号")
        private int rowNumber;

        @ApiModelProperty(value = "城市名称")
        private String cityName;

        @ApiModelProperty(value = "失败原因")
        private String reason;

        public BatchImportFailureDetail() {}

        public BatchImportFailureDetail(int rowNumber, String cityName, String reason) {
            this.rowNumber = rowNumber;
            this.cityName = cityName;
            this.reason = reason;
        }
    }

    public BatchImportResultVO() {}

    public BatchImportResultVO(boolean success, String message, int successCount, int failureCount, List<BatchImportFailureDetail> failureDetails) {
        this.success = success;
        this.message = message;
        this.successCount = successCount;
        this.failureCount = failureCount;
        this.failureDetails = failureDetails;
    }

    /**
     * 创建成功结果
     */
    public static BatchImportResultVO success(int successCount) {
        return new BatchImportResultVO(true, "导入成功", successCount, 0, null);
    }

    /**
     * 创建部分成功结果
     */
    public static BatchImportResultVO partialSuccess(int successCount, int failureCount, List<BatchImportFailureDetail> failureDetails) {
        return new BatchImportResultVO(true, "导入完成，部分数据失败", successCount, failureCount, failureDetails);
    }

    /**
     * 创建失败结果
     */
    public static BatchImportResultVO failure(String message, List<BatchImportFailureDetail> failureDetails) {
        return new BatchImportResultVO(false, message, 0, failureDetails != null ? failureDetails.size() : 0, failureDetails);
    }
}
