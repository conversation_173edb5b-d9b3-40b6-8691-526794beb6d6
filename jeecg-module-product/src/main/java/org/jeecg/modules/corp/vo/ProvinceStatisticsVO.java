package org.jeecg.modules.corp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 省份统计VO
 * 参考ReportCoordinateVO结构，包含城市code、经纬度和预约总数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "省份统计VO", description = "一级省份统计数据")
public class ProvinceStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 行政区划代码（一级城市编码）
     */
    @ApiModelProperty(value = "行政区划代码", example = "130000")
    private String adcode;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称", example = "石家庄市")
    private String name;

    /**
     * 中心坐标 [经度, 纬度]
     */
    @ApiModelProperty(value = "中心坐标 [经度, 纬度]", example = "[114.502461, 38.045474]")
    private List<Double> center;

    /**
     * 质心坐标 [经度, 纬度]
     */
    @ApiModelProperty(value = "质心坐标 [经度, 纬度]", example = "[114.502461, 38.045474]")
    private List<Double> centroid;

    /**
     * 预约总数
     */
    @ApiModelProperty(value = "预约总数")
    private Integer reservationCount;

    public ProvinceStatisticsVO() {
        this.reservationCount = 0;
    }

    public ProvinceStatisticsVO(String adcode, String name) {
        this();
        this.adcode = adcode;
        this.name = name;
    }
}
