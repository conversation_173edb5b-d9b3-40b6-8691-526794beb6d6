package org.jeecg.modules.corp.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.desensitization.util.SensitiveInfoUtil;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdAdded;
import org.jeecg.modules.corp.mapper.PdAddedMapper;
import org.jeecg.modules.corp.service.IPdAddedService;
import org.jeecg.modules.info.entity.PdCasualtyInfo;
import org.jeecg.modules.wechat.dto.config.DeployConfigDTO;
import org.jeecg.modules.wechat.service.ISysDeployConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;

/**
 * @Description: 增值服务预约记录
 * @Author: jeecg-boot
 * @Date:   2024-11-14
 * @Version: V1.0
 */
@Service
public class PdAddedServiceImpl extends ServiceImpl<PdAddedMapper, PdAdded> implements IPdAddedService {

    @Autowired
    private ISysDeployConfigService sysDeployConfigService;

    @Override
    public IPage<PdAdded> pageList(Page<PdAdded> page, LedgerListDto dto) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 检查用户是否有权限查看所有租户数据
        boolean hasAllDataPermission = sysUser != null && sysUser.getAllCheck() != null && sysUser.getAllCheck() == 1;

        // 如果用户有权限查看所有租户数据，则不需要限制租户ID
        if (!hasAllDataPermission) {
            // 用户没有权限查看所有租户数据，按照原来的逻辑限制租户ID
            List<String> tenantIds = Arrays.asList(sysUser.getRelTenantIds().split(","));
            //需要查询用户是否有这个租户的权限
            if (!StrUtil.isEmpty(dto.getTenantId())) {
                // 如果传入的租户ID不在当前用户的租户权限中，则抛出异常或返回错误
                if (!tenantIds.contains(dto.getTenantId())) {
                    dto.setTenantId(null);
                }
                // 如果用户有该租户权限，将其加入dto
                dto.setTenantIds(Collections.singletonList(dto.getTenantId()));
            } else {
                // 如果没有传入租户ID，使用当前用户的所有租户权限
                dto.setTenantIds(tenantIds);
            }
        } else {
            // 用户有权限查看所有租户数据
            // 如果传入了特定的租户ID，则只查询该租户的数据
            if (!StrUtil.isEmpty(dto.getTenantId())) {
                dto.setTenantIds(Collections.singletonList(dto.getTenantId()));
            } else {
                // 如果没有传入租户ID，则不限制租户ID，查询所有租户的数据
                dto.setTenantIds(null);
            }
        }
        if (StrUtil.isNotEmpty(dto.getDateRange())) {
            //将时间字段拆分dateRange
            String[] split = dto.getDateRange().split(",");
            dto.setStartDate(split[0]);
            dto.setEndDate(split[1]);
        }
        Random random = new Random();
        IPage<PdAdded> pageList = this.baseMapper.pageList(page, dto);

        // 获取系统配置
        DeployConfigDTO config = sysDeployConfigService.getDeployConfig();

        pageList.getRecords().forEach(item->{
            if (StrUtil.isEmpty(item.getGuestName())) {
                // 随机生成3位数字
                int randomNum = 100 + random.nextInt(900); // 生成 100-999 的随机数
                // 创建游客用户实例并设置UUID和名称
                String uuid = UUID.randomUUID().toString();
                item.setGuestName("游客"+randomNum+"_" + uuid.substring(0, 8)); // 生成一个格式统一的名称，比如“游客_xxxxxxxx”
            }

            // 处理脱敏
            // 如果数据实现方式是"查询实现脱敏"，并且"去除查询脱敏"是关闭的，则进行脱敏处理
            if ("query".equals(config.getQueryType()) && !Boolean.TRUE.equals(config.getRemoveQueryMask())) {
                // 根据配置进行脱敏处理

                // 姓名脱敏
                if (Boolean.TRUE.equals(config.getNameSwitch()) && StrUtil.isNotEmpty(item.getName())) {
                    // 如果姓名不为空，则将姓名后面的部分替换为"先生"或"女士"
                    if ("男".equals(item.getSex())) {
                        item.setName(item.getName().substring(0, 1) + "先生");
                    } else if ("女".equals(item.getSex())) {
                        item.setName(item.getName().substring(0, 1) + "女士");
                    } else {
                        // 如果性别未知，则使用通用脱敏
                        item.setName(SensitiveInfoUtil.chineseName(item.getName()));
                    }
                }

                // 手机号脱敏
                if (Boolean.TRUE.equals(config.getPhoneSwitch()) && StrUtil.isNotEmpty(item.getPhone())) {
                    // 手机号脱敏：前三位，后四位，其他隐藏
                    item.setPhone(SensitiveInfoUtil.mobilePhone(item.getPhone()));
                }
            }
        });
        return pageList;
    }

    @Override
    public PdAdded queryDetailById(String id) {
        return this.baseMapper.queryDetailById(id);
    }
}
