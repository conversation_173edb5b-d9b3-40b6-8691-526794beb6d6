package org.jeecg.modules.corp.vo.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 地区坐标信息视图对象
 * 包含总部信息和子城市列表
 *
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Data
@Accessors(chain = true)
@ApiModel("地区坐标信息视图对象")
public class RegionCoordinateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总部信息
     */
    @ApiModelProperty(value = "总部信息")
    private HeadquarterVO headquarter;

    /**
     * 子城市列表
     */
    @ApiModelProperty(value = "子城市列表")
    private List<ReportCoordinateVO> businessRegions;

    /**
     * 总部信息VO
     */
    @Data
    @Accessors(chain = true)
    @ApiModel("总部信息")
    public static class HeadquarterVO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 总部名称
         */
        @ApiModelProperty(value = "总部名称", example = "北京总部")
        private String name;

        /**
         * 行政区划代码
         */
        @ApiModelProperty(value = "行政区划代码", example = "110000")
        private String adcode;

        /**
         * 中心坐标 [经度, 纬度]
         */
        @ApiModelProperty(value = "中心坐标 [经度, 纬度]", example = "[116.407526, 39.904030]")
        private List<Double> center;

        /**
         * 质心坐标 [经度, 纬度]
         */
        @ApiModelProperty(value = "质心坐标 [经度, 纬度]", example = "[116.407526, 39.904030]")
        private List<Double> centroid;

        /**
         * 总部编码
         */
        @ApiModelProperty(value = "总部编码")
        private String headquarterCode;
    }
}
