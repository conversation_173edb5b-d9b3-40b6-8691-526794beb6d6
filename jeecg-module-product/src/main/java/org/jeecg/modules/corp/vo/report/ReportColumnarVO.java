package org.jeecg.modules.corp.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ReportColumnarVO implements Serializable {


    @ApiModelProperty(value = "城市名称", example = "石家庄市")
    private String city;

    @ApiModelProperty(value = "一级城市编码", example = "130100")
    private String cityCode;

    @ApiModelProperty(value = "二级城市编码", example = "130100")
    private String secondaryCityCode;

    @ApiModelProperty(value = "预约总数")
    private String resTotal;
}
