package org.jeecg.modules.corp.vo.hour;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 报表汇总数据VO
 */
@Data
@Accessors(chain = true)
public class ReportSummaryVO implements Serializable {

    @ApiModelProperty(value = "访问量(即点击数)")
    private Integer visitCount;

    @ApiModelProperty(value = "预约人次")
    private Integer reservationCount;

    @ApiModelProperty(value = "转化率(%)", example = "15.25")
    private BigDecimal conversionRate;

    /**
     * 构造函数
     */
    public ReportSummaryVO() {
        this.visitCount = 0;
        this.reservationCount = 0;
        this.conversionRate = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 带参数的构造函数
     * @param visitCount 访问量
     * @param reservationCount 预约人次
     */
    public ReportSummaryVO(Integer visitCount, Integer reservationCount) {
        this.visitCount = visitCount != null ? visitCount : 0;
        this.reservationCount = reservationCount != null ? reservationCount : 0;
        this.conversionRate = calculateConversionRate(this.visitCount, this.reservationCount);
    }

    /**
     * 计算转化率
     * @param visitCount 访问量
     * @param reservationCount 预约人次
     * @return 转化率
     */
    private BigDecimal calculateConversionRate(Integer visitCount, Integer reservationCount) {
        if (visitCount == null || visitCount == 0) {
            return BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        
        if (reservationCount == null) {
            return BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        
        return new BigDecimal(reservationCount)
            .multiply(new BigDecimal(100))
            .divide(new BigDecimal(visitCount), 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 设置访问量并重新计算转化率
     * @param visitCount 访问量
     * @return this
     */
    public ReportSummaryVO setVisitCount(Integer visitCount) {
        this.visitCount = visitCount != null ? visitCount : 0;
        this.conversionRate = calculateConversionRate(this.visitCount, this.reservationCount);
        return this;
    }

    /**
     * 设置预约人次并重新计算转化率
     * @param reservationCount 预约人次
     * @return this
     */
    public ReportSummaryVO setReservationCount(Integer reservationCount) {
        this.reservationCount = reservationCount != null ? reservationCount : 0;
        this.conversionRate = calculateConversionRate(this.visitCount, this.reservationCount);
        return this;
    }
}
