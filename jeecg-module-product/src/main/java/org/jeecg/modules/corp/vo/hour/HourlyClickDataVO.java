package org.jeecg.modules.corp.vo.hour;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 按小时点击数据VO
 */
@Data
@Accessors(chain = true)
public class HourlyClickDataVO implements Serializable {

    @ApiModelProperty(value = "时段;若 dto 为天,则为天来汇总,为周同理", example = "08:00-09:00")
    private String timeFrame;

    @ApiModelProperty(value = "查询日期的点击PV")
    private Integer queryClickPv;

    @ApiModelProperty(value = "查询日期的点击数")
    private Integer queryClickNum;

    @ApiModelProperty(value = "对比日期的点击PV")
    private Integer compareClickPv;

    @ApiModelProperty(value = "对比日期的点击数")
    private Integer compareClickNum;

    @ApiModelProperty(value = "小时值(仅按小时汇总时使用)")
    private Integer hour;

    @ApiModelProperty(value = "统计日期(仅按天汇总时使用)")
    private String statDate;

    @ApiModelProperty(value = "周数(仅按周汇总时使用)")
    private String weekNum;

    @ApiModelProperty(value = "月份(仅按月汇总时使用)")
    private String monthNum;

}
