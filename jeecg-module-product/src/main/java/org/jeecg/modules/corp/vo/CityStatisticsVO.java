package org.jeecg.modules.corp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 城市统计VO
 * 参考ProvinceStatisticsVO结构，用于返回省份下子城市的预约统计数据
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "城市统计VO", description = "省份下子城市预约统计数据")
public class CityStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 城市行政区划代码
     */
    @ApiModelProperty(value = "城市行政区划代码", example = "130100")
    private String adcode;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称", example = "石家庄市")
    private String name;

    /**
     * 中心坐标 [经度, 纬度]
     */
    @ApiModelProperty(value = "中心坐标 [经度, 纬度]", example = "[114.502461, 38.045474]")
    private List<Double> center;

    /**
     * 质心坐标 [经度, 纬度]
     */
    @ApiModelProperty(value = "质心坐标 [经度, 纬度]", example = "[114.502461, 38.045474]")
    private List<Double> centroid;

    /**
     * 预约总数
     */
    @ApiModelProperty(value = "预约总数")
    private Integer reservationCount;

    /**
     * 所属省份代码
     */
    @ApiModelProperty(value = "所属省份代码", example = "130000")
    private String provinceCode;

    /**
     * 所属省份名称
     */
    @ApiModelProperty(value = "所属省份名称", example = "河北省")
    private String provinceName;

    public CityStatisticsVO() {
        this.reservationCount = 0;
    }

    public CityStatisticsVO(String adcode, String name) {
        this();
        this.adcode = adcode;
        this.name = name;
    }

    public CityStatisticsVO(String adcode, String name, String provinceCode, String provinceName) {
        this(adcode, name);
        this.provinceCode = provinceCode;
        this.provinceName = provinceName;
    }
}
