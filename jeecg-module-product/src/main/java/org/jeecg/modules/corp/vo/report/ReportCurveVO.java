package org.jeecg.modules.corp.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ReportCurveVO implements Serializable {

    @ApiModelProperty(value = "时段",example = "08:00-09:00")
    private String timeFrame;

    @ApiModelProperty(value = " 点击 pv")
    private String  clickPv;

    @ApiModelProperty(value = "点击数即访问量")
    private String  clickNum;

    @ApiModelProperty(value = "预约总数")
    private String  reservationNum;

    @ApiModelProperty(value = "车险预约数")
    private String  carInsuranceReservationNum;

    @ApiModelProperty(value = "财险预约数")
    private String  travelInsuranceReservationNum;

    @ApiModelProperty(value = "增值服务预约数")
    private String  additionalServiceReservationNum;

    @ApiModelProperty(value = "转化率=预约总数/点击数")
    private BigDecimal reservationRate;

}
