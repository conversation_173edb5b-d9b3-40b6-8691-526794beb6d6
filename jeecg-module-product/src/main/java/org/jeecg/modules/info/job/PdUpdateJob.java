package org.jeecg.modules.info.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.info.entity.PdCarInfo;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.entity.PdGuestUsersRel;
import org.jeecg.modules.info.service.IPdCarInfoService;
import org.jeecg.modules.info.service.IPdGuestUsersRelService;
import org.jeecg.modules.info.service.IPdGuestUsersService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class PdUpdateJob implements Job {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private IPdCarInfoService pdCarInfoService;

    @Resource
    private IPdGuestUsersRelService pdGuestUsersRelService;

    @Resource
    private IPdGuestUsersService pdGuestUsersService;

    private static final String REDIS_PROCESSING_KEY = "pd_update_job:processing";
    private static final int BATCH_SIZE = 500; // 每批处理500条记录

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        log.info("开始执行PdUpdateJob定时任务");

        try {
            // 检查是否有任务正在处理
            if (isProcessing()) {
                log.info("已有任务正在处理中，跳过本次执行");
                return;
            }

            // 设置处理标志
            setProcessing(true);

            // 1. 获取所有需要处理的月份
            List<String> monthsToProcess = getAllMonthsToProcess();
            log.info("本次需要处理的月份列表: {}", monthsToProcess);

            // 2. 遍历处理所有月份
            int totalProcessed = 0;
            for (String month : monthsToProcess) {
                log.info("开始处理月份: {}", month);

                try {
                    int processedCount = processMonthDataWithTransaction(month);
                    totalProcessed += processedCount;

                    log.info("月份 {} 处理完成，处理了 {} 条记录", month, processedCount);
                } catch (Exception e) {
                    log.error("处理月份 {} 时发生异常: {}", month, e.getMessage(), e);
                    // 继续处理下一个月份，不中断整个任务
                }
            }

            log.info("PdUpdateJob执行完成，总共处理了 {} 条记录", totalProcessed);

        } catch (Exception e) {
            log.error("PdUpdateJob执行异常", e);
            throw new JobExecutionException(e);
        } finally {
            // 清除处理标志
            setProcessing(false);
        }
    }

    /**
     * 检查是否有任务正在处理
     */
    private boolean isProcessing() {
        Object value = redisTemplate.opsForValue().get(REDIS_PROCESSING_KEY);
        return "true".equals(String.valueOf(value));
    }

    /**
     * 设置处理标志
     */
    private void setProcessing(boolean processing) {
        if (processing) {
            redisTemplate.opsForValue().set(REDIS_PROCESSING_KEY, "true",
                    java.time.Duration.ofHours(2)); // 2小时超时
        } else {
            redisTemplate.delete(REDIS_PROCESSING_KEY);
        }
    }

    /**
     * 获取所有需要处理的月份（从2024年6月开始到当前月份）
     */
    private List<String> getAllMonthsToProcess() {
        log.info("生成需要处理的月份列表");

        // 从2024年6月开始到当前月份
        LocalDate startDate = LocalDate.of(2024, 6, 1);
        LocalDate currentDate = LocalDate.now();

        List<String> months = new ArrayList<>();
        LocalDate date = startDate;

        // 生成所有需要处理的月份
        while (!date.isAfter(currentDate.withDayOfMonth(1))) {
            String monthStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            months.add(monthStr);
            date = date.plusMonths(1);
        }

        log.info("生成月份列表完成，共 {} 个月份: {}", months.size(), months);
        return months;
    }

    /**
     * 带事务的月份数据处理（每个月份独立事务）
     *
     * @param month 月份字符串，格式：yyyy-MM
     * @return 处理的记录数
     */
    @Transactional(rollbackFor = Exception.class)
    public int processMonthDataWithTransaction(String month) {
        return processMonthData(month);
    }

    /**
     * 处理指定月份的数据
     *
     * @param month 月份字符串，格式：yyyy-MM
     * @return 处理的记录数
     */
    private int processMonthData(String month) {
        log.info("=== 开始处理月份 {} 的数据 ===", month);

        // 1. 查询该月份中guest_name为空的pd_car_info记录
        List<PdCarInfo> emptyGuestNameList = queryEmptyGuestNameByMonth(month);
        if (CollectionUtil.isEmpty(emptyGuestNameList)) {
            log.info("月份 {} 没有需要处理的数据，跳过", month);
            return 0;
        }

        log.info("月份 {} 找到 {} 条需要处理的记录", month, emptyGuestNameList.size());

        // 2. 批量查询关系表，获取已有的关系
        List<String> carInfoIds = emptyGuestNameList.stream()
                .map(PdCarInfo::getId)
                .collect(Collectors.toList());

        Map<String, PdGuestUsersRel> relationMap = queryRelationsByPids(carInfoIds);
        log.info("在关系表中找到 {} 条关系记录", relationMap.size());

        // 3. 分别处理有关系和无关系的记录
        List<PdCarInfo> hasRelationList = new ArrayList<>();
        List<PdCarInfo> noRelationList = new ArrayList<>();

        for (PdCarInfo carInfo : emptyGuestNameList) {
            if (relationMap.containsKey(carInfo.getId())) {
                hasRelationList.add(carInfo);
            } else {
                noRelationList.add(carInfo);
            }
        }

        log.info("月份 {} - 有关系记录: {} 条，无关系记录: {} 条",
                month, hasRelationList.size(), noRelationList.size());

        int processedCount = 0;

        // 4. 处理有关系的记录（赋值guest_id和guest_name）
        if (CollectionUtil.isNotEmpty(hasRelationList)) {
            int relationProcessed = processCarInfoWithRelation(hasRelationList, relationMap);
            processedCount += relationProcessed;
        }

        // 5. 处理无关系的记录（只赋值guest_name）
        if (CollectionUtil.isNotEmpty(noRelationList)) {
            int noRelationProcessed = processCarInfoWithoutRelation(noRelationList);
            processedCount += noRelationProcessed;
        }

        log.info("=== 月份 {} 数据处理完成，共处理 {} 条记录 ===", month, processedCount);
        return processedCount;
    }

    /**
     * 查询指定月份中guest_name为空的pd_car_info记录
     */
    private List<PdCarInfo> queryEmptyGuestNameByMonth(String month) {
        log.info("查询月份 {} 中guest_name为空的记录", month);

        LambdaQueryWrapper<PdCarInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper -> wrapper
                .isNull(PdCarInfo::getGuestName)
                .or()
                .eq(PdCarInfo::getGuestName, ""))
                .apply("DATE_FORMAT(create_time, '%Y-%m') = {0}", month);

        List<PdCarInfo> result = pdCarInfoService.list(queryWrapper);
        log.info("月份 {} 查询到 {} 条guest_name为空的记录", month, result.size());

        return result;
    }

    /**
     * 批量查询关系表
     */
    private Map<String, PdGuestUsersRel> queryRelationsByPids(List<String> pidList) {
        if (CollectionUtil.isEmpty(pidList)) {
            return new HashMap<>();
        }

        LambdaQueryWrapper<PdGuestUsersRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PdGuestUsersRel::getPid, pidList);

        List<PdGuestUsersRel> relationList = pdGuestUsersRelService.list(queryWrapper);

        return relationList.stream()
                .collect(Collectors.toMap(
                        PdGuestUsersRel::getPid,
                        rel -> rel,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 处理有关系的记录（赋值guest_id和guest_name）
     * @return 实际处理的记录数
     */
    private int processCarInfoWithRelation(List<PdCarInfo> carInfoList, Map<String, PdGuestUsersRel> relationMap) {
        log.info("开始处理有关系的记录，共 {} 条", carInfoList.size());

        // 获取所有guest_id
        Set<String> guestIds = relationMap.values().stream()
                .map(PdGuestUsersRel::getGuestId)
                .collect(Collectors.toSet());

        // 批量查询游客信息
        Map<String, PdGuestUsers> guestUsersMap = queryGuestUsersByIds(new ArrayList<>(guestIds));

        List<PdCarInfo> updateList = new ArrayList<>();

        for (PdCarInfo carInfo : carInfoList) {
            PdGuestUsersRel relation = relationMap.get(carInfo.getId());
            if (relation != null) {
                String guestId = relation.getGuestId();
                PdGuestUsers guestUser = guestUsersMap.get(guestId);

                if (guestUser != null) {
                    PdCarInfo updateCarInfo = new PdCarInfo();
                    updateCarInfo.setId(carInfo.getId());
                    updateCarInfo.setGuestId(guestId);
                    updateCarInfo.setGuestName(guestUser.getName());
                    updateList.add(updateCarInfo);

                    log.debug("准备更新车险信息 - carInfoId: {}, guestId: {}, guestName: {}",
                            carInfo.getId(), guestId, guestUser.getName());
                }
            }
        }

        // 分批批量更新
        if (CollectionUtil.isNotEmpty(updateList)) {
            int totalUpdated = 0;
            for (int i = 0; i < updateList.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, updateList.size());
                List<PdCarInfo> batchList = updateList.subList(i, endIndex);

                boolean success = pdCarInfoService.saveOrUpdateBatch(batchList);
                if (success) {
                    totalUpdated += batchList.size();
                    log.info("批量更新有关系的车险信息成功，本批处理 {} 条记录", batchList.size());
                } else {
                    log.error("批量更新有关系的车险信息失败，批次: {}-{}", i, endIndex);
                    throw new RuntimeException("批量更新有关系的车险信息失败");
                }
            }
            log.info("有关系记录更新完成，总共处理 {} 条记录", totalUpdated);
            return totalUpdated;
        }

        return 0;
    }

    /**
     * 处理无关系的记录（只赋值guest_name）
     * @return 实际处理的记录数
     */
    private int processCarInfoWithoutRelation(List<PdCarInfo> carInfoList) {
        log.info("开始处理无关系的记录，共 {} 条", carInfoList.size());

        List<PdCarInfo> updateList = new ArrayList<>();

        for (PdCarInfo carInfo : carInfoList) {
            // 使用IPdGuestUsersService的createGuestName方法生成游客名称
            String guestName = pdGuestUsersService.createGuestName();

            PdCarInfo updateCarInfo = new PdCarInfo();
            updateCarInfo.setId(carInfo.getId());
            updateCarInfo.setGuestName(guestName);
            // 注意：guest_id不赋值
            updateList.add(updateCarInfo);

            log.debug("准备更新车险信息 - carInfoId: {}, guestName: {}",
                    carInfo.getId(), guestName);
        }

        // 分批批量更新
        if (CollectionUtil.isNotEmpty(updateList)) {
            int totalUpdated = 0;
            for (int i = 0; i < updateList.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, updateList.size());
                List<PdCarInfo> batchList = updateList.subList(i, endIndex);

                boolean success = pdCarInfoService.saveOrUpdateBatch(batchList);
                if (success) {
                    totalUpdated += batchList.size();
                    log.info("批量更新无关系的车险信息成功，本批处理 {} 条记录", batchList.size());
                } else {
                    log.error("批量更新无关系的车险信息失败，批次: {}-{}", i, endIndex);
                    throw new RuntimeException("批量更新无关系的车险信息失败");
                }
            }
            log.info("无关系记录更新完成，总共处理 {} 条记录", totalUpdated);
            return totalUpdated;
        }

        return 0;
    }

    /**
     * 批量查询游客信息
     */
    private Map<String, PdGuestUsers> queryGuestUsersByIds(List<String> guestIds) {
        if (CollectionUtil.isEmpty(guestIds)) {
            return new HashMap<>();
        }

        LambdaQueryWrapper<PdGuestUsers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PdGuestUsers::getId, guestIds);

        List<PdGuestUsers> guestUsersList = pdGuestUsersService.list(queryWrapper);

        return guestUsersList.stream()
                .collect(Collectors.toMap(
                        PdGuestUsers::getId,
                        user -> user,
                        (existing, replacement) -> existing
                ));
    }
}
