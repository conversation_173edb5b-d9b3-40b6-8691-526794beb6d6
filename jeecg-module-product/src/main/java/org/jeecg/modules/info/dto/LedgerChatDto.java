package org.jeecg.modules.info.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class LedgerChatDto {

    /**主键*/
    @ApiModelProperty(value = "预约信息主键")
    private java.lang.String id;

    @ApiModelProperty(value = "创建日期/即聊天开始日期")
    private Date createTime;

    @ApiModelProperty(value = "预约信息ip",required = false)
    private String ipAddress;

    @ApiModelProperty(value = "台账类型(0-车险台账;1-财险台账2-增值服务台账)")
    private Integer ledgerType;

}
