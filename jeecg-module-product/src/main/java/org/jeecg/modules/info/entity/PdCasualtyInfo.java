package org.jeecg.modules.info.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 财险预约信息
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
@Data
@TableName("pd_casualty_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="pd_casualty_info对象", description="财险预约信息")
public class PdCasualtyInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    @Excel(name = "时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "链接类型(0-车险;1-财险;2-增值服务)")
    private Integer linkType;
    @ApiModelProperty(value = "ip地址")
    @Excel(name = "ip地址", width = 15)
    private String ipAddress;

    @ApiModelProperty(value = "一级城市code")
    private String cityCodeLevel;

    @ApiModelProperty(value = "游客id")
    private java.lang.String guestId;

    @ApiModelProperty(value = "游客名称")
    @Excel(name = "游客名称", width = 15)
    private  java.lang.String guestName;
	/**用户来源*/
	@Excel(name = "用户来源", width = 15)
    @ApiModelProperty(value = "用户来源")
    private Integer source;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private String name;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String phone;
    @ApiModelProperty(value = "台账 id")
    private java.lang.String ledgerId;
    @ApiModelProperty(value = " 链接 id")
    private String linkId;
	/**选择服务*/
    @ApiModelProperty(value = "服务")
    private String serve;
	/**多租户*/
	@Excel(name = "多租户", width = 15)
    @ApiModelProperty(value = "多租户")
    private Integer tenantId;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;
    @ApiModelProperty(value = "性别")
    private String sex;
    @ApiModelProperty(value = "城市名称")
    @Excel(name = "城市", width = 15)
    private  String city;
    @ApiModelProperty(value = "多租户名称")
    private transient String tenantName;
    @ApiModelProperty(value = "是否已生成")
    private  Integer isVied;
	/**删除*/
    @ApiModelProperty(value = "删除")
    private Integer isDelete;
    /**是否存在聊天用户（0=否，1=是）*/
    private transient Integer hasChatUser;
}
