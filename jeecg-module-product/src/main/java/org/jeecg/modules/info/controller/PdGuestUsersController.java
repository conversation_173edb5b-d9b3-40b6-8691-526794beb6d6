package org.jeecg.modules.info.controller;

import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.service.IPdGuestUsersService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 游客表
 * @Author: jeecg-boot
 * @Date:   2024-11-12
 * @Version: V1.0
 */
@Api(tags="游客表")
@RestController
@RequestMapping("/info/pdGuestUsers")
@Slf4j
public class PdGuestUsersController extends JeecgController<PdGuestUsers, IPdGuestUsersService> {
	@Autowired
	private IPdGuestUsersService pdGuestUsersService;
	/**
	 * 分页列表查询
	 *
	 * @param pdGuestUsers
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "游客表-分页列表查询")
	@ApiOperation(value="游客表-分页列表查询", notes="游客表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<PdGuestUsers>> queryPageList(PdGuestUsers pdGuestUsers,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<PdGuestUsers> queryWrapper = QueryGenerator.initQueryWrapper(pdGuestUsers, req.getParameterMap());
		Page<PdGuestUsers> page = new Page<PdGuestUsers>(pageNo, pageSize);
		IPage<PdGuestUsers> pageList = pdGuestUsersService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param pdGuestUsers
	 * @return
	 */
	@AutoLog(value = "游客表-添加")
	@ApiOperation(value="游客表-添加", notes="游客表-添加")
	@RequiresPermissions("info:pd_guest_users:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdGuestUsers pdGuestUsers) {
		pdGuestUsersService.save(pdGuestUsers);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param pdGuestUsers
	 * @return
	 */
	@AutoLog(value = "游客表-编辑")
	@ApiOperation(value="游客表-编辑", notes="游客表-编辑")
	@RequiresPermissions("info:pd_guest_users:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdGuestUsers pdGuestUsers) {
		pdGuestUsersService.updateById(pdGuestUsers);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "游客表-通过id删除")
	@ApiOperation(value="游客表-通过id删除", notes="游客表-通过id删除")
	@RequiresPermissions("info:pd_guest_users:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdGuestUsersService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "游客表-批量删除")
	@ApiOperation(value="游客表-批量删除", notes="游客表-批量删除")
	@RequiresPermissions("info:pd_guest_users:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdGuestUsersService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @return
	 */
	//@AutoLog(value = "游客表-通过id查询")
	@ApiOperation(value="游客-通过id查询", notes="游客表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdGuestUsers> createGuestUser() {
		PdGuestUsers guestUser = pdGuestUsersService.createGuestUser(null);

		// 返回保存的用户信息
		return Result.OK(guestUser);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdGuestUsers
    */
    @RequiresPermissions("info:pd_guest_users:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdGuestUsers pdGuestUsers) {
        return super.exportXls(request, pdGuestUsers, PdGuestUsers.class, "游客表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("info:pd_guest_users:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdGuestUsers.class);
    }

}
