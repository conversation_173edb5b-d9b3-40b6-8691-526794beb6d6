package org.jeecg.modules.info.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.corp.entity.PdLinkRecode;
import org.jeecg.modules.corp.service.IPdGenerateConfigService;
import org.jeecg.modules.corp.service.IPdLinkRecodeService;
import org.jeecg.modules.info.util.RandomRangeUtil;
import org.jeecg.modules.system.entity.SysTenant;
import org.jeecg.modules.system.service.ISysTenantService;
import org.jeecg.modules.wechat.dto.config.CarInsuranceConfigVO;
import org.jeecg.modules.wechat.service.ISysDeployConfigService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class PdLinkRecodeJob implements Job {

    @Resource
    private IPdLinkRecodeService pdLinkRecodeService;
    @Resource
    private ISysTenantService sysTenantService;
    @Resource
    private IPdGenerateConfigService pdGenerateConfigService;
    @Resource
    private ISysDeployConfigService sysDeployConfigService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    // 浮动百分比，控制变化幅度
    private static final double FLUCTUATION_PERCENT = 0.05;
    // 小数位精度
    private static final int SCALE = 2;

    /**
     * 任务执行
     * 支持传入日期参数进行补数据
     * 参数名: targetDate，格式: yyyy-MM-dd
     */
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {

        try {
            // 获取当前日期，用于查询和创建当天记录
            Date currentDate = DateUtil.beginOfDay(new Date());
            
            // 查询所有有效租户
            List<SysTenant> tenantList = sysTenantService.lambdaQuery()
                    .eq(SysTenant::getDelFlag, 0)
                    .list();

            if (tenantList == null || tenantList.isEmpty()) {
                log.warn("未找到有效租户，跳过配置生成");
                return;
            }
            List<Integer> tenantIds = tenantList.stream().map(SysTenant::getId).collect(Collectors.toList());
            // 查询租户的所有配置
            List<CarInsuranceConfigVO> configList = pdGenerateConfigService.getTenantConfig(tenantIds);
            
            // 按照租户ID和配置类型进行分组
            Map<Integer, Map<Integer, List<CarInsuranceConfigVO>>> tenantConfigMap = null;
            if (!CollectionUtil.isEmpty(configList)) {
                // 首先按租户ID分组
                Map<Integer, List<CarInsuranceConfigVO>> byTenantId = configList.stream()
                        .collect(Collectors.groupingBy(CarInsuranceConfigVO::getTenantId));
                
                // 然后按配置类型再次分组
                tenantConfigMap = byTenantId.entrySet().stream()
                        .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().stream()
                                    .collect(Collectors.groupingBy(CarInsuranceConfigVO::getConfigType))
                        ));
            }
            
            // 获取默认配置Map，按configType分组
            Map<Integer, CarInsuranceConfigVO> defaultConfigMap = null;
            if (CollectionUtil.isEmpty(configList)) {
                // 0-车险、1-财险、2-增值服务
                List<CarInsuranceConfigVO> defaultConfigs = new ArrayList<>();
                // 车险默认配置
                CarInsuranceConfigVO carConfig = sysDeployConfigService.getDeployConfigByType(2, CarInsuranceConfigVO.class);
                if (carConfig != null) {
                    carConfig.setConfigType(0); // 确保类型正确
                    defaultConfigs.add(carConfig);
                }
                
                // 财险默认配置 - 这里假设也是从系统配置中获取，根据实际情况调整
                CarInsuranceConfigVO propertyConfig = sysDeployConfigService.getDeployConfigByType(3, CarInsuranceConfigVO.class);
                if (propertyConfig != null && ObjectUtil.isNotEmpty(propertyConfig.getAvgStayTimeEnd())) {
                    propertyConfig.setConfigType(1);
                    defaultConfigs.add(propertyConfig);
                }
                
                // 增值服务默认配置
                CarInsuranceConfigVO valueAddedConfig = sysDeployConfigService.getDeployConfigByType(4, CarInsuranceConfigVO.class);
                if (valueAddedConfig != null && ObjectUtil.isNotEmpty(valueAddedConfig.getAvgStayTimeEnd())) {
                    valueAddedConfig.setConfigType(2);
                    defaultConfigs.add(valueAddedConfig);
                }
                
                // 转换为Map
                if (!defaultConfigs.isEmpty()) {
                    defaultConfigMap = defaultConfigs.stream()
                            .collect(Collectors.toMap(CarInsuranceConfigVO::getConfigType, config -> config));
                }
            }

            for (SysTenant tenant : tenantList) {
                try {
                    Integer tenantId = tenant.getId();
                    
                    // 获取租户的业务类型
                    String busType = tenant.getBusType();
                    List<Integer> busTypeList = new ArrayList<>();
                    if (StrUtil.isNotBlank(busType)) {
                        // 将业务类型字符串转换为列表
                        busTypeList = Arrays.stream(busType.split(","))
                                .map(Integer::parseInt)
                                .collect(Collectors.toList());
                    } else {
                        // 如果租户没有设置业务类型，默认处理所有类型
                        busTypeList.add(0); // 车险
                        busTypeList.add(1); // 财险
                        busTypeList.add(2); // 增值服务
                    }
                    
                    // 处理每个业务类型
                    for (Integer configType : busTypeList) {
                        // 获取该类型的配置
                        CarInsuranceConfigVO config = null;
                        
                        // 首先尝试获取租户特定的配置
                        if (tenantConfigMap != null && tenantConfigMap.containsKey(tenantId)) {
                            Map<Integer, List<CarInsuranceConfigVO>> typeConfigMap = tenantConfigMap.get(tenantId);
                            if (typeConfigMap.containsKey(configType) && !typeConfigMap.get(configType).isEmpty()) {
                                config = typeConfigMap.get(configType).get(0);
                            }
                        }
                        
                        // 如果没有租户特定配置，使用默认配置
                        if (config == null && defaultConfigMap != null && defaultConfigMap.containsKey(configType)) {
                            config = defaultConfigMap.get(configType);
                        }
                        
                        if (config == null) {
                            log.warn("租户[{}]的业务类型[{}]无配置且无默认配置，跳过处理", tenant.getName(), configType);
                            continue;
                        }

                        
                        // 查询当天是否已存在该类型的记录
                        PdLinkRecode existRecode = pdLinkRecodeService.lambdaQuery()
                                .eq(PdLinkRecode::getTenantId, tenantId)
                                .eq(PdLinkRecode::getLinkDate, currentDate)
                                .eq(PdLinkRecode::getConfigType, configType)
                                .one();
                        
                        if (existRecode == null) {
                            // 不存在则创建新记录
                            createNewRecode(tenantId, currentDate, config, configType);
                        } else {
                            // 存在则更新记录
                            updateExistRecode(existRecode, config);
                        }
                    }

                } catch (Exception e) {
                    log.error("处理租户[{}]配置时发生错误", tenant.getName(), e);
                }
            }

        } catch (Exception e) {
            log.error("执行每日配置生成任务失败", e);
            throw new JobExecutionException(e);
        }
    }
    
    /**
     * 创建新的流量快照记录
     */
    private void createNewRecode(Integer tenantId, Date currentDate, CarInsuranceConfigVO config, Integer configType) {
        PdLinkRecode newRecode = new PdLinkRecode();
        newRecode.setTenantId(tenantId);
        newRecode.setLinkDate(currentDate);
        newRecode.setUpdateTime(new Date());
        newRecode.setConfigType(configType);
        
        // 设置初始值，从配置的区间中随机生成
        setRandomValuesFromConfig(newRecode, config);
        
        // 保存新记录
        pdLinkRecodeService.save(newRecode);
    }
    
    /**
     * 更新已存在的流量快照记录
     */
    private void updateExistRecode(PdLinkRecode existRecode, CarInsuranceConfigVO config) {
        // 更新时间
        existRecode.setUpdateTime(new Date());
        
        // 在区间内对各个值进行小幅度浮动
        updateWithFluctuations(existRecode, config);
        
        // 保存更新
        pdLinkRecodeService.updateById(existRecode);
    }
    
    /**
     * 从配置区间中随机设置值
     */
    private void setRandomValuesFromConfig(PdLinkRecode recode, CarInsuranceConfigVO config) {
        // 跳出率
        recode.setBounceRate(RandomRangeUtil.randomPercent(
                config.getBounceRateStart(), config.getBounceRateEnd(), SCALE));
        
        // 转化率
        recode.setConversionRate(RandomRangeUtil.randomPercent(
                config.getConversionRateStart(), config.getConversionRateEnd(), SCALE));
        
        // 表单提交数
        recode.setFormSubmissions(RandomRangeUtil.randomInRange(
                config.getFormSubmissionsStart(), config.getFormSubmissionsEnd()));
        
        // 点击率
        recode.setCtr(RandomRangeUtil.randomPercent(
                config.getCtrStart(), config.getCtrEnd(), SCALE));
        
        // 平均停留时长
        recode.setAvgStayTime(RandomRangeUtil.randomInRange(
                config.getAvgStayTimeStart(), config.getAvgStayTimeEnd()));
        
        // 返回率
        recode.setReturnRate(RandomRangeUtil.randomPercent(
                config.getReturnRateStart(), config.getReturnRateEnd(), SCALE));
        
        // 内容完成率
        recode.setCompletionRate(RandomRangeUtil.randomPercent(
                config.getCompletionRateStart(), config.getCompletionRateEnd(), SCALE));
        
        // 首屏CTR值
        recode.setFirstScreenCtr(RandomRangeUtil.randomPercent(
                config.getFirstScreenCtrStart(), config.getFirstScreenCtrEnd(), SCALE));
        
        // 内容Jump率
        recode.setContentJumpRate(RandomRangeUtil.randomPercent(
                config.getContentJumpRateStart(), config.getContentJumpRateEnd(), SCALE));
        
        // 内容Return率
        recode.setContentReturnRate(RandomRangeUtil.randomPercent(
                config.getContentReturnRateStart(), config.getContentReturnRateEnd(), SCALE));
        
        // Click深度分布
        recode.setClickDepth(RandomRangeUtil.randomInRange(
                config.getClickDepthStart(), config.getClickDepthEnd(), SCALE));
        
        // Click间隔时间
        recode.setClickIntervalTime(RandomRangeUtil.randomInRange(
                config.getClickIntervalTimeStart(), config.getClickIntervalTimeEnd(), SCALE));
        
        // Engagement得分
        recode.setEngagementScore(RandomRangeUtil.randomInRange(
                config.getEngagementScoreStart(), config.getEngagementScoreEnd(), SCALE));
        
        // 翻页率比例 (注意这里的字段名与其他不同)
        recode.setPageRate(RandomRangeUtil.randomPercent(
                new BigDecimal(config.getPageStartNum()), new BigDecimal(config.getPageEndNum()), SCALE));
        
        // 独立Query数
        recode.setUniqueQuery(RandomRangeUtil.randomInRange(
                config.getUniqueQueryStart(), config.getUniqueQueryEnd()));
        
        // TOP3 PV-CTR
        recode.setTop3PvCtr(RandomRangeUtil.randomPercent(
                config.getTop3PvCtrStart(), config.getTop3PvCtrEnd(), SCALE));
    }
    
    /**
     * 对已有值进行小幅度浮动更新
     */
    private void updateWithFluctuations(PdLinkRecode recode, CarInsuranceConfigVO config) {
        // 跳出率
        recode.setBounceRate(RandomRangeUtil.fluctuatePercent(
                recode.getBounceRate(), config.getBounceRateStart(), 
                config.getBounceRateEnd(), FLUCTUATION_PERCENT, SCALE));
        
        // 转化率
        recode.setConversionRate(RandomRangeUtil.fluctuatePercent(
                recode.getConversionRate(), config.getConversionRateStart(), 
                config.getConversionRateEnd(), FLUCTUATION_PERCENT, SCALE));
        
        // 表单提交数
        recode.setFormSubmissions(RandomRangeUtil.fluctuateInRange(
                recode.getFormSubmissions(), config.getFormSubmissionsStart(), 
                config.getFormSubmissionsEnd(), FLUCTUATION_PERCENT));
        
        // 点击率
        recode.setCtr(RandomRangeUtil.fluctuatePercent(
                recode.getCtr(), config.getCtrStart(), 
                config.getCtrEnd(), FLUCTUATION_PERCENT, SCALE));
        
        // 平均停留时长
        recode.setAvgStayTime(RandomRangeUtil.fluctuateInRange(
                recode.getAvgStayTime(), config.getAvgStayTimeStart(), 
                config.getAvgStayTimeEnd(), FLUCTUATION_PERCENT));
        
        // 返回率
        recode.setReturnRate(RandomRangeUtil.fluctuatePercent(
                recode.getReturnRate(), config.getReturnRateStart(), 
                config.getReturnRateEnd(), FLUCTUATION_PERCENT, SCALE));
        
        // 内容完成率
        recode.setCompletionRate(RandomRangeUtil.fluctuatePercent(
                recode.getCompletionRate(), config.getCompletionRateStart(), 
                config.getCompletionRateEnd(), FLUCTUATION_PERCENT, SCALE));
        
        // 首屏CTR值
        recode.setFirstScreenCtr(RandomRangeUtil.fluctuatePercent(
                recode.getFirstScreenCtr(), config.getFirstScreenCtrStart(), 
                config.getFirstScreenCtrEnd(), FLUCTUATION_PERCENT, SCALE));
        
        // 内容Jump率
        recode.setContentJumpRate(RandomRangeUtil.fluctuatePercent(
                recode.getContentJumpRate(), config.getContentJumpRateStart(), 
                config.getContentJumpRateEnd(), FLUCTUATION_PERCENT, SCALE));
        
        // 内容Return率
        recode.setContentReturnRate(RandomRangeUtil.fluctuatePercent(
                recode.getContentReturnRate(), config.getContentReturnRateStart(), 
                config.getContentReturnRateEnd(), FLUCTUATION_PERCENT, SCALE));
        
        // Click深度分布
        recode.setClickDepth(RandomRangeUtil.fluctuateInRange(
                recode.getClickDepth(), config.getClickDepthStart(), 
                config.getClickDepthEnd(), FLUCTUATION_PERCENT, SCALE));
        
        // Click间隔时间
        recode.setClickIntervalTime(RandomRangeUtil.fluctuateInRange(
                recode.getClickIntervalTime(), config.getClickIntervalTimeStart(), 
                config.getClickIntervalTimeEnd(), FLUCTUATION_PERCENT, SCALE));
        
        // Engagement得分
        recode.setEngagementScore(RandomRangeUtil.fluctuateInRange(
                recode.getEngagementScore(), config.getEngagementScoreStart(), 
                config.getEngagementScoreEnd(), FLUCTUATION_PERCENT, SCALE));
        
        // 翻页率比例 (注意这里的字段名与其他不同)
        recode.setPageRate(RandomRangeUtil.fluctuatePercent(
                recode.getPageRate(), new BigDecimal(config.getPageStartNum()), 
                new BigDecimal(config.getPageEndNum()), FLUCTUATION_PERCENT, SCALE));
        
        // 独立Query数
        recode.setUniqueQuery(RandomRangeUtil.fluctuateInRange(
                recode.getUniqueQuery(), config.getUniqueQueryStart(), 
                config.getUniqueQueryEnd(), FLUCTUATION_PERCENT));
        
        // TOP3 PV-CTR
        recode.setTop3PvCtr(RandomRangeUtil.fluctuatePercent(
                recode.getTop3PvCtr(), config.getTop3PvCtrStart(), 
                config.getTop3PvCtrEnd(), FLUCTUATION_PERCENT, SCALE));
    }
}