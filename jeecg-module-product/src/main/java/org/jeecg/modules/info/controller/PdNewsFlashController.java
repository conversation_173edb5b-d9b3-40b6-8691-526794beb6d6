package org.jeecg.modules.info.controller;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.text.SimpleDateFormat;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.info.entity.PdNewsFlash;
import org.jeecg.modules.info.service.IPdNewsFlashService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.wechat.vo.news.AppnewsListVO;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 新闻快讯
 * @Author: jeecg-boot
 * @Date:   2025-05-05
 * @Version: V1.0
 */
@Api(tags="新闻快讯")
@RestController
@RequestMapping("/info/pdNewsFlash")
@Slf4j
public class PdNewsFlashController extends JeecgController<PdNewsFlash, IPdNewsFlashService> {
	@Autowired
	private IPdNewsFlashService pdNewsFlashService;

	/**
	 * 分页列表查询
	 *
	 * @param pdNewsFlash
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "新闻快讯-分页列表查询")
	@ApiOperation(value="新闻快讯-分页列表查询", notes="新闻快讯-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<PdNewsFlash>> queryPageList(PdNewsFlash pdNewsFlash,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<PdNewsFlash> queryWrapper = QueryGenerator.initQueryWrapper(pdNewsFlash, req.getParameterMap());
		Page<PdNewsFlash> page = new Page<PdNewsFlash>(pageNo, pageSize);
		IPage<PdNewsFlash> pageList = pdNewsFlashService.page(page, queryWrapper);
		return Result.OK(pageList);
	}



	/**
	 *   添加
	 *
	 * @param pdNewsFlash
	 * @return
	 */
	@AutoLog(value = "新闻快讯-添加")
	@ApiOperation(value="新闻快讯-添加", notes="新闻快讯-添加")
	@RequiresPermissions("info:pd_news_flash:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdNewsFlash pdNewsFlash) {
		pdNewsFlashService.save(pdNewsFlash);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param pdNewsFlash
	 * @return
	 */
	@AutoLog(value = "新闻快讯-编辑")
	@ApiOperation(value="新闻快讯-编辑", notes="新闻快讯-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdNewsFlash pdNewsFlash) {
		pdNewsFlashService.updateById(pdNewsFlash);
		return Result.OK("编辑成功!");
	}

	 @ApiOperation(value = "快讯-分页接口", notes = "快讯-分页接口")
	 @GetMapping(value = "/getListVO")
	 public Result<AppnewsListVO> getListVO(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
											  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		 AppnewsListVO resultVO = pdNewsFlashService.getNewsFlashByPage(pageNo, pageSize);
		 return Result.OK(resultVO);
	 }



	 @ApiOperation(value="随机获取一条快讯", notes="随机获取当天的一条快讯，如果当天没有则返回昨天的")
	 @GetMapping(value = "/getNewsRandom")
	 public Result<Map<String, Object>> getNewsRandom() {
		 try {
			 // 获取当天的开始和结束时间
			 Calendar calendar = Calendar.getInstance();
			 calendar.set(Calendar.HOUR_OF_DAY, 0);
			 calendar.set(Calendar.MINUTE, 0);
			 calendar.set(Calendar.SECOND, 0);
			 calendar.set(Calendar.MILLISECOND, 0);
			 Date todayStart = calendar.getTime();

			 calendar.set(Calendar.HOUR_OF_DAY, 23);
			 calendar.set(Calendar.MINUTE, 59);
			 calendar.set(Calendar.SECOND, 59);
			 calendar.set(Calendar.MILLISECOND, 999);
			 Date todayEnd = calendar.getTime();

			 // 查询当天的快讯
			 List<PdNewsFlash> todayNews = pdNewsFlashService.lambdaQuery()
					 .between(PdNewsFlash::getOrderTime, todayStart, todayEnd)
					 .list();

			 // 如果当天没有快讯，则查询昨天的
			 if (todayNews == null || todayNews.isEmpty()) {
				 calendar.setTime(todayStart);
				 calendar.add(Calendar.DAY_OF_MONTH, -1);
				 Date yesterdayStart = calendar.getTime();

				 calendar.setTime(todayEnd);
				 calendar.add(Calendar.DAY_OF_MONTH, -1);
				 Date yesterdayEnd = calendar.getTime();

				 todayNews = pdNewsFlashService.lambdaQuery()
						 .list();
			 }

			 // 如果仍然没有快讯，返回空结果
			 if (todayNews == null || todayNews.isEmpty()) {
				 return Result.OK(null);
			 }

			 // 随机选择一条快讯
			 int randomIndex = new Random().nextInt(todayNews.size());
			 PdNewsFlash randomNews = todayNews.get(randomIndex);

			 // 格式化时间为 HH:mm
			 SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
			 String newsTime = sdf.format(randomNews.getOrderTime());

			 // 构建返回结果
			 Map<String, Object> resultMap = new HashMap<>();
			 resultMap.put("newsTime", newsTime);
			 resultMap.put("content", randomNews.getContent());

			 return Result.OK(resultMap);
		 } catch (Exception e) {
			 log.error("获取随机快讯失败", e);
			 return Result.OK(null);
		 }
	 }

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "新闻快讯-通过id删除")
	@ApiOperation(value="新闻快讯-通过id删除", notes="新闻快讯-通过id删除")
	@RequiresPermissions("info:pd_news_flash:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdNewsFlashService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "新闻快讯-批量删除")
	@ApiOperation(value="新闻快讯-批量删除", notes="新闻快讯-批量删除")
	@RequiresPermissions("info:pd_news_flash:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdNewsFlashService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "新闻快讯-通过id查询")
	@ApiOperation(value="新闻快讯-通过id查询", notes="新闻快讯-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdNewsFlash> queryById(@RequestParam(name="id",required=true) String id) {
		PdNewsFlash pdNewsFlash = pdNewsFlashService.getById(id);
		if(pdNewsFlash==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdNewsFlash);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdNewsFlash
    */
    @RequiresPermissions("info:pd_news_flash:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdNewsFlash pdNewsFlash) {
        return super.exportXls(request, pdNewsFlash, PdNewsFlash.class, "新闻快讯");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("info:pd_news_flash:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdNewsFlash.class);
    }

}
