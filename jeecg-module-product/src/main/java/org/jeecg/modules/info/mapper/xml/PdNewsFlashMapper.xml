<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.info.mapper.PdNewsFlashMapper">

    <!-- 分页查询新闻快讯，按时间降序排列 -->
    <select id="getNewsFlashByPage" resultType="org.jeecg.modules.wechat.vo.news.AppnewsVO">
        SELECT
            pnf.id AS id,
            pnf.content AS name,
            DATE_FORMAT(pnf.order_time, '%H:%i') AS newsTime,
            IFNULL(pnf.hold_num, 0) AS holdNum,
            pnf.news_id AS newsId
        FROM pd_news_flash pnf
        WHERE pnf.order_time IS NOT NULL
        ORDER BY pnf.order_time DESC, pnf.id DESC
    </select>

</mapper>