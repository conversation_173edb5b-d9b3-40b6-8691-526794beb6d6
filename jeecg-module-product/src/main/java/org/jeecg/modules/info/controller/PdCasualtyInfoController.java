package org.jeecg.modules.info.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.corp.dto.LedgerListDto;
import org.jeecg.modules.corp.entity.PdLedger;
import org.jeecg.modules.info.entity.PdCasualtyInfo;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.entity.PdGuestUsersRel;
import org.jeecg.modules.info.service.IPdCasualtyInfoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.info.service.IPdGuestUsersRelService;
import org.jeecg.modules.info.service.IPdGuestUsersService;
import org.jeecg.modules.info.service.TenantFilter;
import org.jeecg.modules.wechat.entity.PdIntegrated;
import org.jeecg.modules.wechat.service.IPdIntegratedService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 财险预约信息
 * @Author: jeecg-boot
 * @Date:   2024-11-02
 * @Version: V1.0
 */
@Api(tags="财险预约信息")
@RestController
@RequestMapping("/info/pdCasualtyInfo")
@Slf4j
public class PdCasualtyInfoController extends JeecgController<PdCasualtyInfo, IPdCasualtyInfoService> {
	@Autowired
	private IPdCasualtyInfoService pdCasualtyInfoService;
	 @Autowired
	 private IPdIntegratedService pdIntegratedService;
	 @Autowired
	 private IPdGuestUsersRelService guestUsersRelService;
	 @Autowired
	 private IPdGuestUsersService pdGuestUsersService;

	/**
	 * 分页列表查询
	 *
	 * @return
	 */
	//@AutoLog(value = "财险预约信息-分页列表查询")
	@ApiOperation(value="财险预约信息-分页列表查询", notes="财险预约信息-分页列表查询")
	@PostMapping(value = "/list/{pageNum}/{pageSize}")
	public Result<IPage<PdCasualtyInfo>> queryPageList(
			@PathVariable(name = "pageNum")Long pageNum,
			@PathVariable(name = "pageSize")Long pageSize,
			@RequestBody LedgerListDto dto) {
		Page<PdCasualtyInfo> page = new Page<>(pageNum, pageSize);
		IPage<PdCasualtyInfo> pageList = pdCasualtyInfoService.pageList(page,dto);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param pdCasualtyInfo
	 * @return
	 */
	@AutoLog(value = "财险预约信息-添加")
	@ApiOperation(value="财险预约信息-添加", notes="财险预约信息-添加")
	@RequiresPermissions("info:pd_casualty_info:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdCasualtyInfo pdCasualtyInfo) {
		pdCasualtyInfoService.save(pdCasualtyInfo);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param pdCasualtyInfo
	 * @return
	 */
	@AutoLog(value = "财险预约信息-编辑")
	@ApiOperation(value="财险预约信息-编辑", notes="财险预约信息-编辑")
	@RequiresPermissions("info:pd_casualty_info:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdCasualtyInfo pdCasualtyInfo) {
		pdCasualtyInfoService.updateById(pdCasualtyInfo);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "财险预约信息-通过id删除")
	@ApiOperation(value="财险预约信息-通过id删除", notes="财险预约信息-通过id删除")
	@RequiresPermissions("info:pd_casualty_info:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdCasualtyInfoService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "财险预约信息-批量删除")
	@ApiOperation(value="财险预约信息-批量删除", notes="财险预约信息-批量删除")
	@RequiresPermissions("info:pd_casualty_info:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdCasualtyInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "财险预约信息-通过id查询")
	@ApiOperation(value="财险预约信息-通过id查询", notes="财险预约信息-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdCasualtyInfo> queryById(@RequestParam(name="id",required=true) String id) {
		PdCasualtyInfo pdCasualtyInfo = pdCasualtyInfoService.queryDetailById(id);
		if(pdCasualtyInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdCasualtyInfo);
	}

    /**
     * 获取导出数据
     *
     * @param dto 查询条件
     * @param page 分页参数
     * @return 查询结果
     */
    private IPage<PdCasualtyInfo> getExportData(LedgerListDto dto, Page<PdCasualtyInfo> page) {
        return pdCasualtyInfoService.pageList(page, dto);
    }

    /**
    * 导出excel
    *
    * @param request
    */
    @RequiresPermissions("info:pd_casualty_info:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, @RequestBody LedgerListDto dto) {
        // 获取导出字段列表（只导出前端展示的字段）
        String exportFields = "createTime,ipAddress,guestName,city,serve,phone,name,tenantName";

        // 获取选中的记录IDs
        String selections = request.getParameter("selections");

        // 获取当前用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 如果有选中记录，则导出选中的记录
        if (oConvertUtils.isNotEmpty(selections)) {
            // 使用选中的IDs进行查询
            List<String> idList = Arrays.asList(selections.split(","));

            // 根据ID列表查询数据
            List<PdCasualtyInfo> exportList = new ArrayList<>();
            for (String id : idList) {
                PdCasualtyInfo item = pdCasualtyInfoService.queryDetailById(id);
                if (item != null) {
                    exportList.add(item);
                }
            }

            // 导出Excel
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.FILE_NAME, "财险预约信息");
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.CLASS, PdCasualtyInfo.class);
            ExportParams exportParams = new ExportParams("财险预约信息", "导出人:" + sysUser.getRealname(), "财险预约信息");
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.PARAMS, exportParams);
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.DATA_LIST, exportList);
            mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.EXPORT_FIELDS, exportFields);
            return mv;
        } else {
            // 如果没有选中记录，则按照查询条件导出
            try {
                // 使用分页查询，确保导出的数据与列表一致
                Page<PdCasualtyInfo> page = new Page<>(1, 999999999); // 设置合适的分页大小
                IPage<PdCasualtyInfo> pageList = getExportData(dto, page);
                List<PdCasualtyInfo> exportList = pageList.getRecords();

                // 导出Excel
                ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.FILE_NAME, "财险预约信息");
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.CLASS, PdCasualtyInfo.class);
                ExportParams exportParams = new ExportParams("财险预约信息", "导出人:" + sysUser.getRealname(), "财险预约信息");
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.PARAMS, exportParams);
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.DATA_LIST, exportList);
                mv.addObject(org.jeecgframework.poi.excel.def.NormalExcelConstants.EXPORT_FIELDS, exportFields);
                return mv;
            } catch (Exception e) {
                log.error("导出Excel异常", e);
                throw new RuntimeException("导出异常,请联系管理员");
            }
        }
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("info:pd_casualty_info:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, PdCasualtyInfo.class);
    }

}
