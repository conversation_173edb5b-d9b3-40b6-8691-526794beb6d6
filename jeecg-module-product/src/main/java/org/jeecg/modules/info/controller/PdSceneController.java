package org.jeecg.modules.info.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.ApiModelProperty;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.info.dto.PdSceneAddDTO;
import org.jeecg.modules.info.entity.PdScene;
import org.jeecg.modules.info.service.IPdSceneService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 场景库
 * @Author: jeecg-boot
 * @Date:   2025-04-22
 * @Version: V1.0
 */
@Api(tags="场景库")
@RestController
@RequestMapping("/info/pdScene")
@Slf4j
public class PdSceneController extends JeecgController<PdScene, IPdSceneService> {
	@Autowired
	private IPdSceneService pdSceneService;

	/**
	 * 分页列表查询
	 *
	 * @param pdScene
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "场景库-分页列表查询")
	@ApiOperation(value="场景库-分页列表查询", notes="场景库-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<PdScene>> queryPageList(PdScene pdScene,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   @RequestParam(name="sourceType", required = false) Integer sourceType,
								   @RequestParam(name="sceneName", required = false) String sceneName,
								   @RequestParam(name="serverType", required = false) String serverType,
								   @RequestParam(name="isUse", required = false) String isUse,
								   HttpServletRequest req) {
		QueryWrapper<PdScene> queryWrapper = new QueryWrapper<>();
		Page<PdScene> page = new Page<PdScene>(pageNo, pageSize);

		// 添加查询条件
		if (sourceType != null) {
			queryWrapper.eq("source_type", sourceType);
		}
		if (sceneName != null && !sceneName.trim().isEmpty()) {
			queryWrapper.like("scene_name", sceneName.trim());
		}
		if (serverType != null && !serverType.trim().isEmpty()) {
			queryWrapper.eq("server_type", serverType.trim());
		}
		if (isUse != null && !isUse.trim().isEmpty()) {
			queryWrapper.eq("is_use", isUse.trim());
		}

		// 按创建时间倒序排列
		queryWrapper.orderByDesc("create_time");

		IPage<PdScene> pageList = pdSceneService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加场景库/姓名库
	 *
	 * @param dto 场景库/姓名库添加DTO
	 * @return
	 */
	@AutoLog(value = "场景库/姓名库-添加")
	@ApiOperation(value="场景库/姓名库-添加", notes="场景库/姓名库-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody PdSceneAddDTO dto) {
		if (dto.getSourceType() == null) {
			return Result.error("类型不能为空");
		}

		List<PdScene> sceneList = new ArrayList<>();

		// 场景库新增
		if (dto.getSourceType() == 1) {
			if (dto.getSceneList() == null || dto.getSceneList().isEmpty()) {
				return Result.error("场景列表不能为空");
			}

			for (String sceneName : dto.getSceneList()) {
				PdScene pdScene = new PdScene();
				pdScene.setSourceType(dto.getSourceType());
				pdScene.setServerType(dto.getServerType());
				pdScene.setSceneName(sceneName);
				pdScene.setIsUse("0"); // 未使用状态
				sceneList.add(pdScene);
			}
		}
		// 姓名库新增
		else if (dto.getSourceType() == 2) {
			if (dto.getNameList() == null || dto.getNameList().isEmpty()) {
				return Result.error("姓名列表不能为空");
			}

			for (String name : dto.getNameList()) {
				PdScene pdScene = new PdScene();
				pdScene.setSourceType(dto.getSourceType());
				pdScene.setSceneName(name);
				pdScene.setIsUse("0"); // 未使用状态
				sceneList.add(pdScene);
			}
		} else {
			return Result.error("不支持的类型");
		}

		if (!sceneList.isEmpty()) {
			pdSceneService.saveBatch(sceneList);
		}

		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param pdScene
	 * @return
	 */
	@AutoLog(value = "场景库-编辑")
	@ApiOperation(value="场景库-编辑", notes="场景库-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody PdScene pdScene) {
		pdSceneService.updateById(pdScene);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "场景库-通过id删除")
	@ApiOperation(value="场景库-通过id删除", notes="场景库-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		pdSceneService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "场景库-批量删除")
	@ApiOperation(value="场景库-批量删除", notes="场景库-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.pdSceneService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "场景库-通过id查询")
	@ApiOperation(value="场景库-通过id查询", notes="场景库-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<PdScene> queryById(@RequestParam(name="id",required=true) String id) {
		PdScene pdScene = pdSceneService.getById(id);
		if(pdScene==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(pdScene);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param pdScene
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, PdScene pdScene) {
        return super.exportXls(request, pdScene, PdScene.class, "场景库");
    }



	 @ApiOperation(value="导入配置")
	 @PostMapping(value = {"/importExcel"})
	 public Result importExcel(@RequestPart("file") MultipartFile file,@RequestParam String sourceType) throws Exception {
		 pdSceneService.importExcel(file,sourceType);
		 return Result.OK("添加成功！");
	 }

	/**
	 * 下载Excel模板文件
	 *
	 * @param fileName 文件名
	 * @param response HTTP响应
	 */
	@AutoLog(value = "场景库-下载模板文件")
	@ApiOperation(value="场景库-下载模板文件", notes="根据文件名下载Excel模板文件")
	@GetMapping(value = "/downloadTemplate")
	public void downloadTemplate(@RequestParam(name="fileName", required=true) String fileName,
								 HttpServletResponse response) {
		try {
			pdSceneService.downloadTemplate(fileName, response);
		} catch (Exception e) {
			log.error("下载模板文件失败: " + e.getMessage(), e);
			response.setStatus(500);
			try {
				response.getWriter().write("下载失败: " + e.getMessage());
			} catch (Exception ex) {
				// 忽略写入异常
			}
		}
	}

}
