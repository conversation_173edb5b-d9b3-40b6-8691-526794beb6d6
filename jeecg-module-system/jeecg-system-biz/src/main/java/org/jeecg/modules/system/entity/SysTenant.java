package org.jeecg.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 租户信息
 * @author: jeecg-boot
 */
@Data
@TableName("sys_tenant")
public class SysTenant implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编码
     */
    private Integer id;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 公司logo
     */
    private String companyLogo;

    @ApiModelProperty(value = "评分")
    private String score;


    @ApiModelProperty(value = "微信小程序 logo")
    private String wechatLogo;

    @ApiModelProperty(value = "业务类型 0车险,1财险 2 增值服务;可多选逗号隔开")
    private String busType;

    @ApiModelProperty(value = "小程序上架展示 1 是2否")
    private String isTop;

    /**
     * 简介
     */
    private String intro;

    /**
     * 名称
     */
    private String name;

    /**
     * 创建人
     */
    @Dict(dictTable ="sys_user",dicText = "realname",dicCode = "username")
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date beginDate;

    /**
     * 结束时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 状态 1正常 0冻结
     */
    @Dict(dicCode = "tenant_status")
    private Integer status;

    @ApiModelProperty(value = "排序")
    private BigDecimal sortOrder;

    @ApiModelProperty(value = "是否显示 1是 0否")
    private Integer isShow;

    /**
     * 所属行业
     */
    @Dict(dicCode = "trade")
    private String trade;

    /**
     * 公司规模
     */
    @Dict(dicCode = "company_size")
    private String companySize;
    private String ipAddress;



    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 工作地点
     */
    private String workPlace;
    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "网址")
    private String website;

    @ApiModelProperty(value = "企业邮箱")
    private String email;




    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    @ApiModelProperty(value = "注册资本")
    private String registeredCapital;

    /**
     * 二级域名(暂时无用,预留字段)
     */
    private String secondaryDomain;

    /**
     * 登录背景图片(暂时无用，预留字段)
     */
    private String loginBkgdImg;

    /**
     * 职级
     */
    @Dict(dicCode = "company_rank")
    private String position;



    @ApiModelProperty(value = "租户分类")
    private String tenantType;

    /**
     * 部门
     */
    @Dict(dicCode = "company_department")
    private String department;

    @TableLogic
    private Integer delFlag;

    /**更新人登录名称*/
    private String updateBy;

    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 允许申请管理员 1允许 0不允许
     */
    private Integer applyStatus;

    /**
     * 注册时间
     */
    private String regTime;  // added field

  // added field

    /**
     * 法人
     */
    private String legalPerson;  // added field
    //查看次数
    private transient String clickNum;  // added field

    /**
     * 历史浏览记录数量
     */
    @ApiModelProperty(value = "历史浏览记录数量")
    private transient Integer browseCount;

    /**
     * 收藏记录数量
     */
    @ApiModelProperty(value = "收藏记录数量")
    private transient Integer favoriteCount;

    /**
     * 点赞记录数量
     */
    @ApiModelProperty(value = "点赞记录数量")
    private transient Integer likeCount;

    /**
     * 分享记录数量
     */
    @ApiModelProperty(value = "分享记录数量")
    private transient Integer shareCount;

}

