package org.jeecg.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.system.dto.*;
import org.jeecg.modules.system.entity.SysTenant;
import org.jeecg.modules.system.vo.SysPdTenantVO;
import org.jeecg.modules.system.vo.tenant.TenantPackUser;
import org.jeecg.modules.system.vo.tenant.TenantPackUserCount;
import org.jeecg.modules.system.vo.tenant.UserDepart;
import org.jeecg.modules.system.vo.tenant.UserPosition;

import java.util.List;
import java.util.Map;

/**
 * @Description: 租户mapper接口
 * @author: jeecg-boot
 */
public interface SysTenantMapper extends BaseMapper<SysTenant> {

    /**
     * 获取最大值id
     */
    @Select("select MAX(id) id FROM sys_tenant")
    Integer getMaxTenantId();

    /**
     * 获取租户回收站的数据假删除
     * @param page
     * @param sysTenant
     * @return
     */
    List<SysTenant> getRecycleBinPageList(@Param("page") Page<SysTenant> page, @Param("sysTenant") SysTenant sysTenant);

    /**
     * 彻底删除租户
     * @param tenantId
     */
    Integer deleteByTenantId(@Param("tenantIds") List<Integer> tenantId);

    /**
     * 租户还原
     * @param list
     * @return
     */
    Integer revertTenantLogic(@Param("tenantIds")List<Integer> list);

    /**
     * 用于统计 租户产品包的人员数量
     * @param tenantId
     * @return
     */
    List<TenantPackUserCount> queryTenantPackUserCount(@Param("tenantId") Integer tenantId);

    /**
     * 查询人员是不是租户产品包的 超级管理员
     * @param tenantId
     * @param userId
     * @return
     */
    Integer querySuperAdminCount(@Param("tenantId") Integer tenantId, @Param("userId") String userId);

    /**
     * 查询人员的产品包编码
     * @param tenantId
     * @param userId
     * @return
     */
    List<String> queryUserPackCode(@Param("tenantId") Integer tenantId, @Param("userId") String userId);

    /**
     * 查询产品包关联的用户列表
     * @param tenantId
     * @param packId
     * @param packUserStatus
     * @return
     */
    List<TenantPackUser> queryPackUserList(@Param("tenantId") Integer tenantId, @Param("packId") String packId, @Param("packUserStatus") Integer packUserStatus);


    /**
     * 根据用户ID 查询部门
     * @param userIdList
     * @return
     */
    List<UserDepart> queryUserDepartList(@Param("userIdList") List<String> userIdList);

    /**
     * 根据用户ID 查询职位
     * @param userIdList
     * @return
     */
    List<UserPosition> queryUserPositionList(@Param("userIdList") List<String> userIdList);

    /**
     * 查询产品包关联的用户列表
     * @param page
     * @param tenantId
     * @param packId
     * @param status
     * @return
     */
    List<TenantPackUser> queryTenantPackUserList(@Param("page") Page<TenantPackUser> page, @Param("tenantId") String tenantId, @Param("packId") String packId, @Param("status") Integer status);


    /**
     * 根据租户ID 查询租户
     * @param id
     * @return
     */
    @Select("select * from sys_tenant where id = #{id}")
    SysTenant querySysTenant(@Param("id") Integer id);

    /**
     * 查看是否已经申请过了超级管理员
     * @param userId
     * @param tenantId
     * @return
     */
    Long getApplySuperAdminCount(@Param("userId") String userId, @Param("tenantId") Integer tenantId);

    /**
     * 租户是否存在
     * @param tenantId
     * @return
     */
    @Select("select count(1) from sys_tenant where id = #{tenantId} and del_flag = 0")
    Long tenantIzExist(@Param("tenantId") Integer tenantId);

    /**
     * 根据用户id获取租户
     * @param userId
     * @return
     */
    List<SysTenant> getTenantListByUserId(@Param("userId") String userId);

    String getNameClick(@Param("id") String id);

    List<SysTenantDetailsVO> selectTenantDetailsList(@Param("tenantType") String tenantType, @Param("excludeId") Integer excludeId, @Param("currentCityCode") String currentCityCode, @Param("parentCityCode") String parentCityCode);

    SysTenantDetailsVO selectRandomTenant(@Param("tenantType") String tenantType, @Param("currentCityCode") String currentCityCode, @Param("parentCityCode") String parentCityCode);

    /**
     * 分页查询租户详情列表（排除指定租户）
     * @param page 分页参数
     * @param tenantType 租户类型
     * @param currentCityCode 当前城市编码（可选）
     * @param parentCityCode 上级城市编码（可选）
     * @param tenantName 租户名称（可选）
     * @param excludeId 要排除的租户ID
     * @return 分页租户详情列表
     */
    IPage<SysTenantDetailsVO> selectTenantDetailsPageListExclude(Page<SysTenantDetailsVO> page,
                                                                @Param("tenantType") String tenantType,
                                                                @Param("currentCityCode") String currentCityCode,
                                                                @Param("parentCityCode") String parentCityCode,
                                                                @Param("tenantName") String tenantName,
                                                                @Param("excludeId") Integer excludeId);

    /**
     * 新增点击数，返回主键
     *
     * @param num 点击数
     * @param id
     * @return 点击记录ID
     */
    void saveWhClick(@Param("num") Integer num, @Param("pid") String pid, String id);

    /**
     * 修改点击数
     * @param clicksNum 点击数
     */
    void updateWhClick(@Param("clicksNum") Integer clicksNum, @Param("pid") String pid);

    /**
     * 插入公司信息与点击量关联
     * @param name 公司名称
     * @param companyId 公司ID
     * @param clicksId 点击记录ID
     */
    void insertFirmInformation(@Param("name") String name, @Param("companyId") Integer companyId, @Param("clicksId") String clicksId);

    /**
     * 根据公司ID获取点击ID
     * @return 点击记录ID
     */
    String getClicksIdByCompanyId(@Param("pid") Integer pid);

    IPage<SysTenantPortalVO> queryList(Page<SysTenantPortalVO> page, @Param("dto") SysAddTenantUserDto dto);

    IPage<SysTenant> queryPageList(Page<SysTenant> page, @Param("dto")SysTenantDto dto);

    /**
     * @return 分页结果
     */
    IPage<SysTenantVO> getCompanyDirectory(Page<SysTenantVO> page, @Param("dto") SysTenantDto dto);

    void removeByIdList(@Param("idList") List<Integer> idList);

    List<SysPdTenantVO> queryAllList(@Param("dto") SysTenantUserDto dto);

    /**
     * 获取小程序配置的服务商列表
     * @param tenantType 租户类型（可选）
     * @param currentCityCode 当前城市编码（可选）
     * @param parentCityCode 上级城市编码（可选）
     * @return 服务商列表
     */
    List<SysTenantDetailsVO> getMiniProgramServiceProviders(@Param("tenantType") String tenantType, @Param("currentCityCode") String currentCityCode, @Param("parentCityCode") String parentCityCode);

    /**
     * 获取所有租户服务商列表
     * @param tenantType 租户类型（可选）
     * @return 服务商列表
     */
    List<SysTenantDetailsVO> getAllTenantServiceProviders(@Param("tenantType") String tenantType);

    /**
     * 根据城市匹配获取服务商列表
     * @param tenantType 租户类型（可选）
     * @param currentCityCode 当前城市编码
     * @param parentCityCode 上级城市编码
     * @return 服务商列表
     */
    List<SysTenantDetailsVO> getCityMatchServiceProviders(@Param("tenantType") String tenantType, @Param("currentCityCode") String currentCityCode, @Param("parentCityCode") String parentCityCode);

    /**
     * 根据城市名称查询城市编码和上级城市编码
     * @param cityName 城市名称
     * @return 包含当前城市编码和上级城市编码的Map
     */
    Map<String, String> getCityCodesWithParent(@Param("cityName") String cityName);

    /**
     * 根据配置类型查询部署配置
     * @param deployType 配置类型
     * @return 配置JSON字符串
     */
    String getDeployConfigByType(@Param("deployType") Integer deployType);

    /**
     * 根据内容ID获取统计信息
     * @param pid 内容ID
     * @return 统计信息数组 [browseCount, favoriteCount, likeCount, shareCount]
     */
    Integer[] getRecordStatisticsByPid(@Param("pid") String pid);
}
