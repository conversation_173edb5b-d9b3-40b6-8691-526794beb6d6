package org.jeecg.modules.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.dto.*;
import org.jeecg.modules.system.entity.SysTenant;
import org.jeecg.modules.system.entity.SysTenantPackUser;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.vo.SysPdTenantVO;
import org.jeecg.modules.system.vo.config.MiniProgramConfigVO;
import org.jeecg.modules.system.vo.tenant.TenantDepartAuthInfo;
import org.jeecg.modules.system.vo.tenant.TenantDetailsListVO;
import org.jeecg.modules.system.vo.tenant.TenantPackModel;
import org.jeecg.modules.system.vo.tenant.TenantPackUser;
import org.jeecg.modules.system.vo.tenant.TenantPackUserCount;

import java.util.Collection;
import java.util.List;

/**
 * @Description: 租户service接口
 * @author: jeecg-boot
 */
public interface ISysTenantService extends IService<SysTenant> {

    /**
     * 查询有效的租户
     *
     * @param idList
     * @return
     */
    List<SysTenant> queryEffectiveTenant(Collection<Integer> idList);

    /**
     * 返回某个租户被多少个用户引用了
     *
     * @param id
     * @return
     */
    Long countUserLinkTenant(String id);

    /**
     * 根据ID删除租户，会判断是否已被引用
     *
     * @param id
     * @return
     */
    boolean removeTenantById(String id);

    /**
     * 邀请用户加入租户,通过手机号
     * @param ids
     * @param phone
     */
    void invitationUserJoin(String ids, String phone);

    /**
     * 请离用户（租户）
     * @param userIds
     * @param tenantId
     */
    void leaveTenant(String userIds, String tenantId);

    /**
     * 添加租户，并将创建的用户加入关系表
     * @param sysTenant
     * @param userId
     */
    Integer saveTenantJoinUser(SysTenant sysTenant, String userId);

    /**
     * 保存租户
     * @param sysTenant
     */
    void saveTenant(SysTenant sysTenant);

    /**
     * 通过门牌号加入租户
     * @param sysTenant
     * @param userId
     */
    Integer joinTenantByHouseNumber(SysTenant sysTenant, String userId);

    /**
     * 统计一个人创建了多少个租户
     *
     * @param userId
     * @return
     */
    Integer countCreateTenantNum(String userId);

    /**
     * 获取租户回收站的数据
     * @param page
     * @param sysTenant
     * @return
     */
    IPage<SysTenant> getRecycleBinPageList(Page<SysTenant> page, SysTenant sysTenant);

    /**
     * 彻底删除租户
     * @param ids
     */
    void deleteTenantLogic(String ids);

    /**
     * 还原租户
     * @param ids
     */
    void revertTenantLogic(String ids);

    /**
     * 退出租户
     * @param userId
     * @param userId
     * @param username
     */
    void exitUserTenant(String userId, String username, String tenantId);

    /**
     * 变更租户拥有者
     * @param userId
     * @param tenantId
     */
    void changeOwenUserTenant(String userId, String tenantId);

    /**
     * 邀请用户到租户。通过手机号匹配
     * @param phone
     * @param departId
     * @return
     */
    Result<String> invitationUser(String phone, String departId);

    /**
     * 进入应用组织页面 查询用户是否有 超级管理员的权限
     * @param tenantId
     * @return
     */
    TenantDepartAuthInfo getTenantDepartAuthInfo(Integer tenantId);


    /**
     * 获取 租户产品包-3个默认admin的人员数量
     * @param tenantId
     * @return
     */
    List<TenantPackUserCount> queryTenantPackUserCount(Integer tenantId);

    /**
     * 查询租户产品包信息
     * @param model
     * @return
     */
    TenantPackModel queryTenantPack(TenantPackModel model);

    /**
     * 添加多个用户和产品包的关系数据
     * @param sysTenantPackUser
     */
    void addBatchTenantPackUser(SysTenantPackUser sysTenantPackUser);

    /**
     * 添加用户和产品包的关系数据 带日志记录的
     * @param sysTenantPackUser
     */
    void addTenantPackUser(SysTenantPackUser sysTenantPackUser);

    /**
     * 移除用户和产品包的关系数据 带日志记录的
     * @param sysTenantPackUser
     */
    void deleteTenantPackUser(SysTenantPackUser sysTenantPackUser);


    /**
     * 查询申请的用户列表
     */
    List<TenantPackUser> getTenantPackApplyUsers(Integer tenantId);

    /**
     * 个人 申请成为管理员
     * @param sysTenantPackUser
     */
    void doApplyTenantPackUser(SysTenantPackUser sysTenantPackUser);

    /**
     * 申请通过 成为管理员
     * @param sysTenantPackUser
     */
    void passApply(SysTenantPackUser sysTenantPackUser);

    /**
     * 拒绝申请 成为管理员
     * @param sysTenantPackUser
     */
    void deleteApply(SysTenantPackUser sysTenantPackUser);

    /**
     * 产品包用户列表
     * @param tenantId
     * @param packId
     * @param status
     * @param page
     * @return
     */
    IPage<TenantPackUser> queryTenantPackUserList(String tenantId, String packId, Integer status, Page<TenantPackUser> page);

    /**
     * 查看是否已经申请过了管理员
     * @return
     */
    Long getApplySuperAdminCount();

    /**
     * 发送同意或者拒绝消息
     *
     * @param user
     * @param content
     */
    void sendMsgForAgreeAndRefuseJoin(SysUser user, String content);

    /**
     * 根据密码删除当前用户
     *
     * @param sysUser
     * @param tenantId
     */
    void deleteUserByPassword(SysUser sysUser, Integer tenantId);

    /**
     * 根据用户id获取租户信息
     * @param userId
     * @return
     */
    List<SysTenant> getTenantListByUserId(String userId);

    String getNameClick(String id);



    /**
     * 获取租户详情列表，包含推荐租户和分页租户列表（排除推荐租户）
     * @param tenantType 租户类型
     * @param city 城市参数（可选）
     * @param tenantName 租户名称（可选）
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @return 包含推荐租户和分页租户列表的VO
     */
    TenantDetailsListVO getTenantDetailsWithRecommendAndPage(String tenantType, String city, String tenantName, Integer pageNo, Integer pageSize);

    /**
     * 门户查询租户列表
     * @param page
     * @param dto
     * @return
     */
    IPage<SysTenantPortalVO> queryList(Page<SysTenantPortalVO> page, SysAddTenantUserDto dto);

    IPage<SysTenant> queryPageList(Page<SysTenant> page, SysTenantDto dto);

    /**
     * 获取公司目录（只返回id和name以及序号）
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<org.jeecg.modules.system.dto.SysTenantVO> getCompanyDirectory(Page<org.jeecg.modules.system.dto.SysTenantVO> page, SysTenantDto dto);

    void removeByIdList(List<Integer> idList);

    List<SysPdTenantVO> queryAllList(SysTenantUserDto dto);

    /**
     * 获取小程序配置的服务商列表
     * @param tenantType 租户类型（可选）
     * @param city 城市（可选）
     * @return 服务商列表
     */
    List<SysTenantDetailsVO> getMiniProgramServiceProviders(String tenantType, String city);

    /**
     * 根据优先级配置获取服务商列表
     * 按照配置的优先级顺序进行查询，如果某个优先级查询到结果就直接返回，没有查询到才进行下一个优先级的查询
     * @param tenantType 租户类型（可选）
     * @param currentCityCode 当前城市编码（可选）
     * @param parentCityCode 上级城市编码（可选）
     * @return 服务商列表
     */
    List<SysTenantDetailsVO> getServiceProvidersByPriority(String tenantType, String currentCityCode, String parentCityCode);

    /**
     * 从sys_deploy_config表查询配置并解析为小程序配置VO
     * @param deployType 配置类型
     * @return 小程序配置VO
     */
    MiniProgramConfigVO getDeployConfigByTypeOne(Integer deployType);

    /**
     * 根据内容ID获取统计信息
     * @param pid 内容ID
     * @return 统计信息数组 [browseCount, favoriteCount, likeCount, shareCount]
     */
    Integer[] getRecordStatisticsByPid(String pid);
}
